<?php

namespace Shaqi\Portfolio\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \Shaqi\Portfolio\Models\PortfolioTag
 */
class TagResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
