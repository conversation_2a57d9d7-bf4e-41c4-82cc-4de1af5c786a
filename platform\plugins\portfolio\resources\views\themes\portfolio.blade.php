<div>
    <h3>{{ $portfolio->name }}</h3>
    {!! Theme::breadcrumb()->render() !!}
</div>
<header>
    <h3>{{ $portfolio->name }}</h3>
    <div>
        @if ($portfolio->categories->isNotEmpty())
            <span>
                <a href="{{ $portfolio->categories->first()->url }}">{{ $portfolio->categories->first()->name }}</a>
            </span>
        @endif
        <span>{{ $portfolio->created_at->format('M d, Y') }}</span>

        @if ($portfolio->tags->isNotEmpty())
            <span>
                @foreach ($portfolio->tags as $tag)
                    <a href="{{ $tag->url }}">{{ $tag->name }}</a>
                @endforeach
            </span>
        @endif
    </div>
</header>

@if ($portfolio->image)
    <div class="portfolio-featured-image">
        <img src="{{ RvMedia::getImageUrl($portfolio->image) }}" alt="{{ $portfolio->name }}" class="img-fluid">
    </div>
@endif

@if ($portfolio->website_link)
    <div class="portfolio-website-link">
        <a href="{{ $portfolio->website_link }}" target="_blank" class="btn btn-primary">
            {{ __('Visit Website') }}
        </a>
    </div>
@endif

@if ($portfolio->logo)
    <div class="portfolio-logo">
        <img src="{{ RvMedia::getImageUrl($portfolio->logo) }}" alt="{{ $portfolio->name }} Logo" class="portfolio-logo-img">
    </div>
@endif

<div class='ck-content'>
    {!! BaseHelper::clean($portfolio->content) !!}
</div>

@if ($portfolio->features && count($portfolio->features) > 0)
    <div class="portfolio-features">
        <h4>{{ __('Features') }}</h4>
        <ul>
            @foreach ($portfolio->features as $feature)
                <li>{{ $feature }}</li>
            @endforeach
        </ul>
    </div>
@endif

@if ($portfolio->gallery && count($portfolio->gallery) > 0)
    <div class="portfolio-gallery">
        <h4>{{ __('Gallery') }}</h4>
        <div class="row">
            @foreach ($portfolio->gallery as $image)
                <div class="col-md-4 col-sm-6 mb-3">
                    <img src="{{ RvMedia::getImageUrl($image) }}" alt="{{ $portfolio->name }}" class="img-fluid">
                </div>
            @endforeach
        </div>
    </div>
@endif

<br />
{!! apply_filters(BASE_FILTER_PUBLIC_COMMENT_AREA, null, $portfolio) !!}

@php $relatedPortfolios = get_related_portfolios($portfolio->getKey(), 2); @endphp

@if ($relatedPortfolios->isNotEmpty())
    <footer>
        <h4>{{ __('Related Portfolios') }}</h4>
        @foreach ($relatedPortfolios as $relatedItem)
            <div>
                <article>
                    <div><a href="{{ $relatedItem->url }}"></a>
                        <img
                            src="{{ RvMedia::getImageUrl($relatedItem->image, null, false, RvMedia::getDefaultImage()) }}"
                            alt="{{ $relatedItem->name }}"
                        >
                    </div>
                    <header><a href="{{ $relatedItem->url }}"> {{ $relatedItem->name }}</a></header>
                </article>
            </div>
        @endforeach
    </footer>
@endif
