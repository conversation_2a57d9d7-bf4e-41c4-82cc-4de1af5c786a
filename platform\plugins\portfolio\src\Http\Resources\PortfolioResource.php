<?php

namespace Shaqi\Portfolio\Http\Resources;

use <PERSON>haqi\Media\Facades\RvMedia;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \Shaqi\Portfolio\Models\Portfolio
 */
class PortfolioResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'content' => $this->content,
            'image' => $this->image ? RvMedia::getImageUrl($this->image) : null,
            'logo' => $this->logo ? RvMedia::getImageUrl($this->logo) : null,
            'website_link' => $this->website_link,
            'features' => $this->features,
            'gallery' => $this->gallery ? collect($this->gallery)->map(function ($image) {
                return RvMedia::getImageUrl($image);
            })->toArray() : [],
            'is_featured' => $this->is_featured,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'categories' => $this->categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                ];
            }),
            'tags' => $this->tags->map(function ($tag) {
                return [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'slug' => $tag->slug,
                    'description' => $tag->description,
                ];
            }),
        ];
    }
}
