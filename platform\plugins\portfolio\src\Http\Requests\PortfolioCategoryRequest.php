<?php

namespace Shaqi\Portfolio\Http\Requests;

use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PortfolioCategoryRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:120',
            'description' => 'nullable|string|max:400',
            'parent_id' => 'nullable|integer|exists:portfolio_categories,id',
            'icon' => 'nullable|string|max:60',
            'is_featured' => 'nullable|boolean',
            'order' => 'nullable|integer|min:0|max:127',
            'is_default' => 'nullable|boolean',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }
}
