<?php

namespace Shaqi\Portfolio\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \Shaqi\Portfolio\Models\PortfolioCategory
 */
class CategoryResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'parent_id' => $this->parent_id,
            'icon' => $this->icon,
            'is_featured' => $this->is_featured,
            'is_default' => $this->is_default,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
