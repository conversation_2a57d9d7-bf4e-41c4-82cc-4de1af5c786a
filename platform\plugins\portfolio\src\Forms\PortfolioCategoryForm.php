<?php

namespace <PERSON><PERSON>qi\Portfolio\Forms;

use Shaqi\Base\Forms\FieldOptions\DescriptionFieldOption;
use Shaqi\Base\Forms\FieldOptions\HiddenFieldOption;
use Shaqi\Base\Forms\FieldOptions\IsFeaturedFieldOption;
use Shaqi\Base\Forms\FieldOptions\NameFieldOption;
use Shaqi\Base\Forms\FieldOptions\OnOffFieldOption;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Base\Forms\FieldOptions\StatusFieldOption;
use Shaqi\Base\Forms\Fields\HiddenField;
use Shaqi\Base\Forms\Fields\OnOffField;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TextareaField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\FormAbstract;
use Shaqi\Portfolio\Http\Requests\PortfolioCategoryRequest;
use <PERSON><PERSON>qi\Portfolio\Models\PortfolioCategory;

class PortfolioCategoryForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(PortfolioCategory::class)
            ->setValidatorClass(PortfolioCategoryRequest::class)
            ->add(
                'order',
                HiddenField::class,
                HiddenFieldOption::make()
                    ->value(function () {
                        if ($this->getModel()->exists) {
                            return $this->getModel()->order;
                        }

                        return PortfolioCategory::query()
                                ->whereIn('parent_id', [0, null])
                                ->latest('order')
                                ->value('order') + 1;
                    })
            )
            ->add('name', TextField::class, NameFieldOption::make()->required()->maxLength(120))
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'parent_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('core/base::forms.parent'))
                    ->choices(
                        PortfolioCategory::query()
                            ->wherePublished()
                            ->where('id', '!=', $this->getModel()->getKey())
                            ->pluck('name', 'id')
                            ->prepend(trans('plugins/portfolio::categories.none'), 0)
                            ->all()
                    )
                    ->selected(old('parent_id', $this->getModel()->parent_id ?: 0))
                    ->addAttribute('class', 'select-search-full')
            )
            ->add(
                'is_featured',
                OnOffField::class,
                IsFeaturedFieldOption::make()
            )
            ->add(
                'is_default',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/portfolio::categories.form.is_default'))
                    ->defaultValue(false)
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
