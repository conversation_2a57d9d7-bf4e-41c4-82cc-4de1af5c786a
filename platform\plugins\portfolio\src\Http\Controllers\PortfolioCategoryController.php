<?php

namespace <PERSON><PERSON>qi\Portfolio\Http\Controllers;

use <PERSON>haqi\ACL\Models\User;
use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use Shaqi\Base\Events\UpdatedContentEvent;
use <PERSON>haqi\Base\Facades\Assets;
use Shaqi\Base\Facades\PageTitle;
use Shaqi\Base\Http\Controllers\BaseController;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\Base\Supports\RepositoryHelper;
use Shaqi\Portfolio\Forms\PortfolioCategoryForm;
use Shaqi\Portfolio\Http\Requests\PortfolioCategoryRequest;
use Shaqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioCategoryInterface;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PortfolioCategoryController extends BaseController
{
    public function __construct(protected PortfolioCategoryInterface $categoryRepository)
    {
    }

    public function index(Request $request)
    {
        PageTitle::setTitle(trans('plugins/portfolio::categories.menu'));

        $categories = PortfolioCategory::query()
            ->orderByDesc('is_default')
            ->oldest('order')->oldest()
            ->with('slugable');

        $categories = RepositoryHelper::applyBeforeExecuteQuery($categories, new PortfolioCategory())->get();

        if ($request->ajax()) {
            $data = view('core/base::forms.partials.tree-categories', $this->getOptions(compact('categories')))
                ->render();

            return $this
                ->httpResponse()
                ->setData($data);
        }

        Assets::addStylesDirectly('vendor/core/core/base/css/tree-category.css')
            ->addScriptsDirectly('vendor/core/core/base/js/tree-category.js');

        return view('plugins/portfolio::categories.index', compact('categories'));
    }

    public function create()
    {
        PageTitle::setTitle(trans('plugins/portfolio::categories.create'));

        return PortfolioCategoryForm::create()->renderForm();
    }

    public function store(PortfolioCategoryRequest $request)
    {
        if ($request->input('is_default')) {
            PortfolioCategory::query()->where('id', '>', 0)->update(['is_default' => 0]);
        }

        $form = PortfolioCategoryForm::create();
        $form
            ->saving(function (PortfolioCategoryForm $form) use ($request): void {
                $form
                    ->getModel()
                    ->fill([...$request->validated(),
                        'author_id' => Auth::guard()->id(),
                        'author_type' => User::class,
                    ])
                    ->save();
            });

        $response = $this->httpResponse();

        /**
         * @var PortfolioCategory $category
         */
        $category = $form->getModel();

        if ($request->ajax()) {
            if ($response->isSaving()) {
                $form = $this->getForm();
            } else {
                $form = $this->getForm($category);
            }

            $response->setData([
                'model' => $category,
                'form' => $form,
            ]);
        }

        event(new CreatedContentEvent(PORTFOLIO_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

        return $response
            ->setPreviousUrl(route('portfolio-categories.index'))
            ->setNextUrl(route('portfolio-categories.edit', $category->getKey()))
            ->withCreatedSuccessMessage();
    }

    public function edit(PortfolioCategory $category)
    {
        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $category->name]));

        return PortfolioCategoryForm::createFromModel($category)->renderForm();
    }

    public function update(PortfolioCategory $category, PortfolioCategoryRequest $request): BaseHttpResponse
    {
        if ($request->input('is_default')) {
            PortfolioCategory::query()->where('id', '!=', $category->id)->update(['is_default' => 0]);
        }

        $form = PortfolioCategoryForm::createFromModel($category);
        $form
            ->saving(function (PortfolioCategoryForm $form) use ($request): void {
                $form
                    ->getModel()
                    ->fill($request->validated())
                    ->save();
            });

        event(new UpdatedContentEvent(PORTFOLIO_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('portfolio-categories.index'))
            ->withUpdatedSuccessMessage();
    }

    public function destroy(PortfolioCategory $category): BaseHttpResponse
    {
        try {
            $category->delete();

            event(new DeletedContentEvent(PORTFOLIO_CATEGORY_MODULE_SCREEN_NAME, request(), $category));

            return $this
                ->httpResponse()
                ->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    protected function getForm(?PortfolioCategory $model = null): string
    {
        $options = [
            'canCreate' => Auth::guard()->user()->hasPermission('portfolio-categories.create'),
            'canEdit' => Auth::guard()->user()->hasPermission('portfolio-categories.edit'),
            'canDelete' => Auth::guard()->user()->hasPermission('portfolio-categories.destroy'),
            'createRoute' => 'portfolio-categories.create',
            'editRoute' => 'portfolio-categories.edit',
            'deleteRoute' => 'portfolio-categories.destroy',
        ];

        if ($model) {
            $form = PortfolioCategoryForm::createFromModel($model, $options);
        } else {
            $form = PortfolioCategoryForm::create($options);
        }

        return $form->renderForm();
    }

    protected function getOptions(array $data = []): array
    {
        return array_merge([
            'canCreate' => Auth::guard()->user()->hasPermission('portfolio-categories.create'),
            'canEdit' => Auth::guard()->user()->hasPermission('portfolio-categories.edit'),
            'canDelete' => Auth::guard()->user()->hasPermission('portfolio-categories.destroy'),
            'createRoute' => 'portfolio-categories.create',
            'editRoute' => 'portfolio-categories.edit',
            'deleteRoute' => 'portfolio-categories.destroy',
            'itemTemplate' => 'plugins/portfolio::categories.item',
            'itemFormTemplate' => 'plugins/portfolio::categories.form',
        ], $data);
    }
}
