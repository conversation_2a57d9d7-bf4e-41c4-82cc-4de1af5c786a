<?php

namespace <PERSON>haqi\Portfolio\Http\Controllers\API;

use <PERSON>haqi\Api\Http\Controllers\BaseController;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Portfolio\Http\Resources\ListPortfolioResource;
use <PERSON>haqi\Portfolio\Http\Resources\PortfolioResource;
use Shaqi\Portfolio\Models\Portfolio;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioInterface;
use Illuminate\Http\Request;

class PortfolioController extends BaseController
{
    public function __construct(protected PortfolioInterface $portfolioRepository)
    {
    }

    /**
     * List portfolios
     *
     * @group Portfolio
     */
    public function index(Request $request)
    {
        $data = $this->portfolioRepository
            ->advancedGet([
                'with' => ['tags', 'categories', 'author', 'slugable'],
                'condition' => ['status' => BaseStatusEnum::PUBLISHED],
                'paginate' => [
                    'per_page' => $request->integer('per_page', 10),
                    'current_paged' => $request->integer('page', 1),
                ],
            ]);

        return $this
            ->httpResponse()
            ->setData(ListPortfolioResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Search portfolios
     *
     * @group Portfolio
     */
    public function getSearch(Request $request)
    {
        $request->validate([
            'q' => 'required|string',
        ]);

        $data = $this->portfolioRepository->getSearch(
            $request->input('q'),
            0,
            $request->integer('per_page', 12)
        );

        return $this
            ->httpResponse()
            ->setData(ListPortfolioResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Filters portfolios
     *
     * @group Portfolio
     */
    public function getFilters(Request $request)
    {
        $filters = [
            'per_page' => $request->integer('per_page', 12),
            'order_by' => $request->input('order_by', 'created_at'),
            'order' => $request->input('order', 'DESC'),
        ];

        $data = $this->portfolioRepository->getFilters($filters);

        return $this
            ->httpResponse()
            ->setData(ListPortfolioResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Get portfolio by slug
     *
     * @group Portfolio
     */
    public function findBySlug(string $slug)
    {
        $portfolio = Portfolio::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->whereHas('slugable', function ($query) use ($slug) {
                $query->where('key', $slug);
            })
            ->with(['categories', 'tags', 'author'])
            ->first();

        if (! $portfolio) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('Not found');
        }

        return $this
            ->httpResponse()
            ->setData(new PortfolioResource($portfolio))
            ->toApiResponse();
    }
}
