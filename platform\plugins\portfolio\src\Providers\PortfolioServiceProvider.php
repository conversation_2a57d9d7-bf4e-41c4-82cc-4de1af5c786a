<?php

namespace Shaqi\Portfolio\Providers;

use Shaqi\ACL\Models\User;
use Shaqi\Api\Facades\ApiHelper;
use Shaqi\Base\Facades\DashboardMenu;
use Shaqi\Base\Facades\PanelSectionManager;
use Shaqi\Base\PanelSections\PanelSectionItem;
use Shaqi\Base\Supports\DashboardMenuItem;
use Shaqi\Base\Supports\ServiceProvider;
use Shaqi\Base\Traits\LoadAndPublishDataTrait;
use Shaqi\Portfolio\Models\Portfolio;
use Shaqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Models\PortfolioTag;
use Shaqi\Portfolio\Repositories\Eloquent\PortfolioRepository;
use Shaqi\Portfolio\Repositories\Eloquent\PortfolioCategoryRepository;
use Shaqi\Portfolio\Repositories\Eloquent\PortfolioTagRepository;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioInterface;
use <PERSON><PERSON><PERSON>\Portfolio\Repositories\Interfaces\PortfolioCategoryInterface;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioTagInterface;
use Shaqi\DataSynchronize\PanelSections\ExportPanelSection;
use Shaqi\DataSynchronize\PanelSections\ImportPanelSection;
use Shaqi\Language\Facades\Language;
use Shaqi\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Shaqi\PluginManagement\Events\DeactivatedPlugin;
use Shaqi\PluginManagement\Events\RemovedPlugin;
use Shaqi\SeoHelper\Facades\SeoHelper;
use Shaqi\Setting\PanelSections\SettingOthersPanelSection;
use Shaqi\Shortcode\View\View;
use Shaqi\Slug\Facades\SlugHelper;
use Shaqi\Slug\Models\Slug;
use Shaqi\Theme\Events\ThemeRoutingBeforeEvent;
use Shaqi\Theme\Facades\SiteMapManager;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;

/**
 * @since 16/07/2025 02:36 AM
 */
class PortfolioServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(PortfolioInterface::class, function () {
            return new PortfolioRepository(new Portfolio());
        });

        $this->app->bind(PortfolioCategoryInterface::class, function () {
            return new PortfolioCategoryRepository(new PortfolioCategory());
        });

        $this->app->bind(PortfolioTagInterface::class, function () {
            return new PortfolioTagRepository(new PortfolioTag());
        });
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/portfolio')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'general'])
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();

        if (class_exists('ApiHelper') && ApiHelper::enabled()) {
            $this->loadRoutes(['api']);
        }

        $this->app->register(EventServiceProvider::class);

        $this->app['events']->listen(ThemeRoutingBeforeEvent::class, function (): void {
            SiteMapManager::registerKey([
                'portfolio-categories',
                'portfolio-tags',
                'portfolio-portfolios-((?:19|20|21|22)\d{2})-(0?[1-9]|1[012])',
            ]);
        });

        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-portfolio')
                        ->priority(5)
                        ->parentId('cms-plugins')
                        ->name('plugins/portfolio::base.menu_name')
                        ->icon('fas fa-briefcase')
                        ->url(route('portfolios.index'))
                        ->permissions(['portfolios.index'])
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-portfolio-post')
                        ->priority(1)
                        ->parentId('cms-plugins-portfolio')
                        ->name('plugins/portfolio::portfolios.menu')
                        ->url(route('portfolios.index'))
                        ->permissions(['portfolios.index'])
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-portfolio-categories')
                        ->priority(2)
                        ->parentId('cms-plugins-portfolio')
                        ->name('plugins/portfolio::categories.menu')
                        ->url(route('portfolio-categories.index'))
                        ->permissions(['portfolio-categories.index'])
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-portfolio-tags')
                        ->priority(3)
                        ->parentId('cms-plugins-portfolio')
                        ->name('plugins/portfolio::tags.menu')
                        ->url(route('portfolio-tags.index'))
                        ->permissions(['portfolio-tags.index'])
                );
        });

        if (defined('LANGUAGE_MODULE_SCREEN_NAME')) {
            if (defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME')) {
                // add language advanced
                LanguageAdvancedManager::registerModule(Portfolio::class, [
                    'name',
                    'description',
                    'content',
                ]);

                LanguageAdvancedManager::registerModule(PortfolioCategory::class, [
                    'name',
                    'description',
                ]);

                LanguageAdvancedManager::registerModule(PortfolioTag::class, [
                    'name',
                    'description',
                ]);
            } else {
                Language::registerModule([Portfolio::class, PortfolioCategory::class, PortfolioTag::class]);
            }
        }

        SlugHelper::registerModule(Portfolio::class, 'Portfolios');
        SlugHelper::registerModule(PortfolioCategory::class, 'Portfolio Categories');
        SlugHelper::registerModule(PortfolioTag::class, 'Portfolio Tags');
        SlugHelper::setPrefix(Portfolio::class, 'portfolio', true);
        SlugHelper::setPrefix(PortfolioCategory::class, 'portfolio-category', true);
        SlugHelper::setPrefix(PortfolioTag::class, 'portfolio-tag', true);

        Event::listen([DeactivatedPlugin::class, RemovedPlugin::class], function ($event): void {
            if ($event->plugin->getId() === 'shaqi/portfolio') {
                PanelSectionManager::default()->removeItem('portfolio-settings');
            }
        });

        PanelSectionManager::default()->beforeRendering(function (): void {
            PanelSectionManager::registerItem(
                'portfolio-settings',
                PanelSectionItem::make('portfolio-settings')
                    ->setTitle(trans('plugins/portfolio::base.settings.title'))
                    ->withIcon('fas fa-briefcase')
                    ->withDescription(trans('plugins/portfolio::base.settings.description'))
                    ->withPriority(120)
                    ->withPermission('portfolio.settings')
                    ->withRoute('portfolio.settings')
            )->setGroupId(SettingOthersPanelSection::getGroupId());

            if (defined('DATA_SYNCHRONIZE_MODULE_SCREEN_NAME')) {
                PanelSectionManager::registerItem(
                    'export-portfolios',
                    PanelSectionItem::make('export-portfolios')
                        ->setTitle('Portfolios')
                        ->withIcon('fas fa-briefcase')
                        ->withPermission('portfolios.export')
                        ->withRoute('tools.data-synchronize.export.portfolios.index')
                        ->withDescription(trans('plugins/portfolio::tools.export_import_description'))
                )->setGroupId(ExportPanelSection::getGroupId());

                PanelSectionManager::registerItem(
                    'import-portfolios',
                    PanelSectionItem::make('import-portfolios')
                        ->setTitle('Portfolios')
                        ->withIcon('fas fa-briefcase')
                        ->withPermission('portfolios.import')
                        ->withRoute('tools.data-synchronize.import.portfolios.index')
                        ->withDescription(trans('plugins/portfolio::tools.export_import_description'))
                )->setGroupId(ImportPanelSection::getGroupId());
            }
        });

        User::resolveRelationUsing('slugable', function (User $user) {
            return $user->morphMany(Slug::class, 'reference');
        });

        $this->app->booted(function (): void {
            SeoHelper::registerModule([Portfolio::class, PortfolioCategory::class, PortfolioTag::class]);

            $configKey = 'packages.revision.general.supported';
            config()->set($configKey, array_merge(config($configKey, []), [Portfolio::class]));

            $this->app->register(HookServiceProvider::class);
        });

        if (function_exists('shortcode')) {
            view()->composer([
                'plugins/portfolio::themes.portfolio',
                'plugins/portfolio::themes.category',
                'plugins/portfolio::themes.tag',
            ], function (View $view): void {
                $view->withShortcodes();
            });
        }
    }
}
