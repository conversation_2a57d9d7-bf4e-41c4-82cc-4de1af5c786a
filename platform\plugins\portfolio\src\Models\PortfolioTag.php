<?php

namespace <PERSON><PERSON>qi\Portfolio\Models;

use <PERSON>haqi\Base\Casts\SafeContent;
use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PortfolioTag extends BaseModel
{
    protected $table = 'portfolio_tags_table';

    protected $fillable = [
        'name',
        'description',
        'status',
        'author_id',
        'author_type',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
    ];

    protected static function booted(): void
    {
        static::deleted(function (PortfolioTag $tag): void {
            $tag->portfolios()->detach();
        });
    }

    public function portfolios(): BelongsToMany
    {
        return $this->belongsToMany(Portfolio::class, 'portfolio_tags', 'tag_id', 'portfolio_id');
    }
}
