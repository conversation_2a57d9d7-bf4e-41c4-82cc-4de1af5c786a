<?php

return [
    'menu' => 'Portfolio Categories',
    'create' => 'New category',
    'edit' => 'Edit category',
    'list' => 'List categories',
    'none' => 'None',
    'form' => [
        'name' => 'Name',
        'name_placeholder' => 'Category\'s name (Maximum 120 characters)',
        'description' => 'Description',
        'description_placeholder' => 'Short description for category (Maximum 400 characters)',
        'parent' => 'Parent',
        'icon' => 'Icon',
        'icon_placeholder' => 'Ex: fa fa-home',
        'order' => 'Order',
        'is_featured' => 'Is featured?',
        'is_default' => 'Is default?',
        'status' => 'Status',
    ],
    'item' => 'Category',
    'model' => 'Category',
    'select' => '-- Select --',
    'cannot_delete' => 'Category could not be deleted',
    'category_deleted' => 'Category deleted',
    'menu_name' => 'Portfolio Categories',
];
