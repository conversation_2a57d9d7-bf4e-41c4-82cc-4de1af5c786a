<?php

namespace <PERSON><PERSON><PERSON>\Portfolio;

use <PERSON><PERSON><PERSON>\Dashboard\Models\DashboardWidget;
use <PERSON>haqi\Menu\Models\MenuNode;
use <PERSON>haqi\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON>qi\Portfolio\Models\Portfolio;
use <PERSON>haqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Models\PortfolioTag;
use Shaqi\Widget\Models\Widget;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('portfolio_tags');
        Schema::dropIfExists('portfolio_category_pivot');
        Schema::dropIfExists('portfolios');
        Schema::dropIfExists('portfolio_categories');
        Schema::dropIfExists('portfolio_tags_table');
        Schema::dropIfExists('portfolios_translations');
        Schema::dropIfExists('portfolio_categories_translations');
        Schema::dropIfExists('portfolio_tags_translations');

        Widget::query()
            ->where('widget_id', 'widget_portfolios_recent')
            ->each(fn (DashboardWidget $dashboardWidget) => $dashboardWidget->delete());

        MenuNode::query()
            ->whereIn('reference_type', [PortfolioCategory::class, PortfolioTag::class])
            ->each(fn (MenuNode $menuNode) => $menuNode->delete());
    }
}
