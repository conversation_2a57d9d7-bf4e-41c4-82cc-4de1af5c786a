<?php

namespace <PERSON><PERSON>qi\Portfolio\Providers;

use <PERSON>haqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use <PERSON><PERSON>qi\Base\Events\UpdatedContentEvent;
use <PERSON><PERSON>qi\Portfolio\Listeners\CreatedContentListener;
use <PERSON><PERSON>qi\Portfolio\Listeners\DeletedContentListener;
use <PERSON><PERSON>qi\Portfolio\Listeners\UpdatedContentListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        CreatedContentEvent::class => [
            CreatedContentListener::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
    ];
}
