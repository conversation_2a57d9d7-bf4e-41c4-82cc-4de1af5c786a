<?php

use <PERSON><PERSON><PERSON>\Portfolio\Repositories\Interfaces\PortfolioInterface;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioCategoryInterface;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioTagInterface;
use Illuminate\Support\Collection;

if (! function_exists('get_featured_portfolios')) {
    function get_featured_portfolios(int $limit = 5, array $with = ['slugable']): Collection
    {
        return app(PortfolioInterface::class)->getFeatured($limit, $with);
    }
}

if (! function_exists('get_recent_portfolios')) {
    function get_recent_portfolios(int $limit = 5, int|string $categoryId = 0): Collection
    {
        return app(PortfolioInterface::class)->getRecentPortfolios($limit, $categoryId);
    }
}

if (! function_exists('get_related_portfolios')) {
    function get_related_portfolios(int|string $portfolioId, int $limit = 3): Collection
    {
        return app(PortfolioInterface::class)->getRelated($portfolioId, $limit);
    }
}

if (! function_exists('get_portfolios_by_category')) {
    function get_portfolios_by_category(array|int|string $categoryId, int $paginate = 12, int $limit = 0): Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return app(PortfolioInterface::class)->getByCategory($categoryId, $paginate, $limit);
    }
}

if (! function_exists('get_portfolios_by_tag')) {
    function get_portfolios_by_tag(int|string $tagId, int $paginate = 12): Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return app(PortfolioInterface::class)->getByTag($tagId, $paginate);
    }
}

if (! function_exists('get_all_portfolios')) {
    function get_all_portfolios(int $perPage = 12, bool $active = true): Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return app(PortfolioInterface::class)->getAllPortfolios($perPage, $active);
    }
}

if (! function_exists('get_featured_portfolio_categories')) {
    function get_featured_portfolio_categories(int $limit = 5, array $with = ['slugable']): Collection
    {
        return app(PortfolioCategoryInterface::class)->getFeaturedCategories($limit, $with);
    }
}

if (! function_exists('get_all_portfolio_categories')) {
    function get_all_portfolio_categories(array $condition = [], array $with = []): Collection
    {
        return app(PortfolioCategoryInterface::class)->getAllCategories($condition, $with);
    }
}

if (! function_exists('get_popular_portfolio_categories')) {
    function get_popular_portfolio_categories(int $limit = 10): Collection
    {
        return app(PortfolioCategoryInterface::class)->getPopularCategories($limit);
    }
}

if (! function_exists('get_popular_portfolio_tags')) {
    function get_popular_portfolio_tags(int $limit = 10): Collection
    {
        return app(PortfolioTagInterface::class)->getPopularTags($limit);
    }
}

if (! function_exists('get_all_portfolio_tags')) {
    function get_all_portfolio_tags(bool $active = true): Collection
    {
        return app(PortfolioTagInterface::class)->getAllTags($active);
    }
}
