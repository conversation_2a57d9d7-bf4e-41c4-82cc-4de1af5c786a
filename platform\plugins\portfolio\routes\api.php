<?php

use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => 'api',
    'prefix' => 'api/v1',
    'namespace' => 'Shaqi\Portfolio\Http\Controllers\API',
], function (): void {
    Route::get('search', 'PortfolioController@getSearch');
    Route::get('portfolios', 'PortfolioController@index');
    Route::get('portfolio-categories', 'PortfolioCategoryController@index');
    Route::get('portfolio-tags', 'PortfolioTagController@index');

    Route::get('portfolios/filters', 'PortfolioController@getFilters');
    Route::get('portfolios/{slug}', 'PortfolioController@findBySlug');
    Route::get('portfolio-categories/filters', 'PortfolioCategoryController@getFilters');
    Route::get('portfolio-categories/{slug}', 'PortfolioCategoryController@findBySlug');
});
