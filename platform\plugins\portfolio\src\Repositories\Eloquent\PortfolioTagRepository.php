<?php

namespace Shaqi\Portfolio\Repositories\Eloquent;

use Shaqi\Portfolio\Repositories\Interfaces\PortfolioTagInterface;
use Shaqi\Support\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Support\Collection;

class PortfolioTagRepository extends RepositoriesAbstract implements PortfolioTagInterface
{
    public function getDataSiteMap(): Collection
    {
        $data = $this->model
            ->with('slugable')
            ->wherePublished()
            ->orderByDesc('created_at')
            ->select(['id', 'name', 'updated_at']);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getPopularTags(int $limit, array $with = ['slugable'], array $withCount = ['portfolios']): Collection
    {
        $data = $this->model
            ->with($with)
            ->withCount($withCount)
            ->orderByDesc('portfolios_count')
            ->limit($limit);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getAllTags(bool $active = true): Collection
    {
        $data = $this->model->select(['id', 'name']);

        if ($active) {
            $data = $data->wherePublished();
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }
}
