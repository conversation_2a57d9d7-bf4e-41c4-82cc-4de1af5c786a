{"__meta": {"id": "X389953b30bdad40c7ee96f515a21baf2", "datetime": "2025-07-15 21:33:27", "utime": 1752615207.126195, "method": "GET", "uri": "/ppc", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752615179.970284, "end": 1752615207.126238, "duration": 27.155954122543335, "duration_str": "27.16s", "measures": [{"label": "Booting", "start": 1752615179.970284, "relative_start": 0, "end": 1752615185.289569, "relative_end": 1752615185.289569, "duration": 5.319284915924072, "duration_str": "5.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752615185.289605, "relative_start": 5.319320917129517, "end": 1752615207.126243, "relative_end": 5.0067901611328125e-06, "duration": 21.83663821220398, "duration_str": "21.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43764096, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "theme.goalconversion::views.page", "param_count": null, "params": [], "start": 1752615198.198168, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/views/page.blade.phptheme.goalconversion::views.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fviews%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}}, {"name": "theme.goalconversion::layouts.ppc", "param_count": null, "params": [], "start": 1752615198.33011, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/ppc.blade.phptheme.goalconversion::layouts.ppc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Flayouts%2Fppc.blade.php&line=1", "ajax": false, "filename": "ppc.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.header", "param_count": null, "params": [], "start": 1752615198.96268, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/header.blade.phptheme.goalconversion::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "packages/theme::partials.header", "param_count": null, "params": [], "start": 1752615199.830476, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.seo-pricing-table", "param_count": null, "params": [], "start": 1752615200.586648, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/seo-pricing-table.blade.phptheme.goalconversion::partials.seo-pricing-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Fseo-pricing-table.blade.php&line=1", "ajax": false, "filename": "seo-pricing-table.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.contact-form", "param_count": null, "params": [], "start": 1752615204.801409, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/contact-form.blade.phptheme.goalconversion::partials.contact-form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Fcontact-form.blade.php&line=1", "ajax": false, "filename": "contact-form.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.footer", "param_count": null, "params": [], "start": 1752615205.318678, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/footer.blade.phptheme.goalconversion::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "packages/theme::partials.footer", "param_count": null, "params": [], "start": 1752615205.879558, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET {slug?}", "middleware": "web, core", "controller": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getView", "namespace": null, "prefix": "", "where": [], "as": "public.single", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=44\" onclick=\"\">vendor/shaqi/theme/src/Http/Controllers/PublicController.php:44-93</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08105, "accumulated_duration_str": "81.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs` where (`key` = 'ppc' and `prefix` = '') limit 1", "type": "query", "params": [], "bindings": ["ppc", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.52402, "duration": 0.01483, "duration_str": "14.83ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 18.297}, {"sql": "select * from `pages` where (`id` = 17 and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": [17, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 18, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.652555, "duration": 0.02014, "duration_str": "20.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 18.297, "width_percent": 24.849}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (17) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/shaqi/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 24, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.863116, "duration": 0.01376, "duration_str": "13.76ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 43.146, "width_percent": 16.977}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (17) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "vendor/shaqi/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.1002, "duration": 0.0076100000000000004, "duration_str": "7.61ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 60.123, "width_percent": 9.389}, {"sql": "select * from `contact_custom_fields` where `status` = 'published' order by `order` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/contact/src/Forms/Fronts/ContactForm.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\contact\\src\\Forms\\Fronts\\ContactForm.php", "line": 95}, {"index": 17, "namespace": null, "name": "platform/plugins/contact/src/Forms/Fronts/ContactForm.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\contact\\src\\Forms\\Fronts\\ContactForm.php", "line": 317}, {"index": 18, "namespace": null, "name": "platform/plugins/contact/src/Forms/Fronts/ContactForm.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\contact\\src\\Forms\\Fronts\\ContactForm.php", "line": 91}, {"index": 19, "namespace": null, "name": "vendor/shaqi/platform/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php", "line": 100}], "start": 1752615203.933321, "duration": 0.02471, "duration_str": "24.71ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 69.513, "width_percent": 30.487}]}, "models": {"data": {"Shaqi\\Slug\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\Base\\Models\\MetaBox": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Shaqi\\Page\\Models\\Page": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Tgc9BwIqGRZxrKLm6KR2CrsVDm9x2aO36ZC4t49y", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/ppc\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/ppc", "status_code": "<pre class=sf-dump id=sf-dump-1158570842 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1158570842\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1651127054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1651127054\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1048494549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1048494549\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1051567411 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://goalconversion.gc/seo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"828 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; XSRF-TOKEN=eyJpdiI6IkhJUWZnNFpDYkkzNkZtQWM3aS9EWlE9PSIsInZhbHVlIjoiQ3duMkRpK3BiQnJVREhhWndPYmN3cXNybjE3MjZ3YnRBN2hoWE8vNFArQ2lGLzBZRWEzeHRoODdJWnVqQVlrVkVzV3krTGJzQnNrK1dBVHdPZWtJNDIxazZpbWNZbWxEWjVOTzZCbkRyejN5c1JEdFhpVDhaSjJnL3BCcjZXeVMiLCJtYWMiOiJhZTFkNDZiZDExODVmYzkyNzBhOTRjNThkNWJjZDllNzE1ZjVkN2VhYzFjNDc3NmZmNWNiMWM1YThkMDRjOTE4IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IjNFYzg0cGdFSno4aFJPOFUxazZEc3c9PSIsInZhbHVlIjoiZ1cyUkcyYXpRT3RqL1dMQ0xBUk9aTS9va0FEUkpMYVIrdC9lSEQrMmFSM1Q2ZS8rU0dYQ0phQnJxVkxWQXpTNVF3QXp0TzhCTGRhZGF4TnhGU1R2a1pabUU0STNTcVJjNE5qbzQ2UWJoN0RiTWRSSEZER3BhQzZobmlOQnI2S2YiLCJtYWMiOiJiYWNmOWRkMmY2MDA3MzRjOGJhNjAzYWUwZDZiM2Y1ODZkNzVlYjMwYzljZDc0OWM0ZmYzYmE4MmM2ZjkzMTZlIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752610562$o32$g1$t1752610627$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051567411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-962823751 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Tgc9BwIqGRZxrKLm6KR2CrsVDm9x2aO36ZC4t49y</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2hPpdFS7hdaen1mBVnwLGUMsxKGJCXpDvjfwuzhh</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962823751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 21:33:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.5.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">Yes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6InFwWHcvKzZoQVpjT3dZZWtHcW11YXc9PSIsInZhbHVlIjoiTmZrY3NHaDhhU1hLUWJ4dDVXNWI5RGJiRzRxZ3VPNTRvL0JDVklZVlNDSVZ2SS9CZ3NSeVFLRWhRK292Sm0wZ25JRmsvQm5lak9ESm9IZmtacXlOd05OU1hBMStGRFRRZjBhRUVYSFBtNFc2TVh6UDNuSS96cy9lOWpLME1oS3kiLCJtYWMiOiJkMTY4MjM0OTljNTc4ZTgxMzkzMTYwNDhlNWQ0Mzc4YjNiYWUyYTE5YWJjOWY2MWM0OWJmZjY3NTUyOTczODM3IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 23:33:26 GMT; Max-Age=7199; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6Im1zSThnRHNINU03ZGxCTHd0ZGxHNHc9PSIsInZhbHVlIjoicVlqUzIwU01NQjRWL0ExNGtuZXBFRVFnVFVkQlBjMVJwb2lyRDhITjdSVHo2T2FqS3lCRUdndjdJT1crN1FFRDBaSzJvNkVZcG5zcWdCT08xVmhoZ0xESDlMVEVmc2tUMFM0WHhKTTdFaWhISDJtN2dXTGJJK3VObmxidGpuSHMiLCJtYWMiOiIwODliN2YwOTk2Y2Q4ZGQ3ZDI4MThmZWMyNDQ1ZDNmZTgyYzg1OGM1NTgzYjI4NzQ1YTNjNmUyMzU3NWEzZWE1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 23:33:26 GMT; Max-Age=7199; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6InFwWHcvKzZoQVpjT3dZZWtHcW11YXc9PSIsInZhbHVlIjoiTmZrY3NHaDhhU1hLUWJ4dDVXNWI5RGJiRzRxZ3VPNTRvL0JDVklZVlNDSVZ2SS9CZ3NSeVFLRWhRK292Sm0wZ25JRmsvQm5lak9ESm9IZmtacXlOd05OU1hBMStGRFRRZjBhRUVYSFBtNFc2TVh6UDNuSS96cy9lOWpLME1oS3kiLCJtYWMiOiJkMTY4MjM0OTljNTc4ZTgxMzkzMTYwNDhlNWQ0Mzc4YjNiYWUyYTE5YWJjOWY2MWM0OWJmZjY3NTUyOTczODM3IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 23:33:26 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6Im1zSThnRHNINU03ZGxCTHd0ZGxHNHc9PSIsInZhbHVlIjoicVlqUzIwU01NQjRWL0ExNGtuZXBFRVFnVFVkQlBjMVJwb2lyRDhITjdSVHo2T2FqS3lCRUdndjdJT1crN1FFRDBaSzJvNkVZcG5zcWdCT08xVmhoZ0xESDlMVEVmc2tUMFM0WHhKTTdFaWhISDJtN2dXTGJJK3VObmxidGpuSHMiLCJtYWMiOiIwODliN2YwOTk2Y2Q4ZGQ3ZDI4MThmZWMyNDQ1ZDNmZTgyYzg1OGM1NTgzYjI4NzQ1YTNjNmUyMzU3NWEzZWE1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 23:33:26 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-543740909 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Tgc9BwIqGRZxrKLm6KR2CrsVDm9x2aO36ZC4t49y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">https://goalconversion.gc/ppc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-543740909\", {\"maxDepth\":0})</script>\n"}}