<?php

namespace Shaqi\Portfolio\Http\Controllers\API;

use Shaqi\Api\Http\Controllers\BaseController;
use <PERSON>haqi\Portfolio\Http\Resources\CategoryResource;
use Shaqi\Portfolio\Http\Resources\ListCategoryResource;
use <PERSON><PERSON>qi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioCategoryInterface;
use Illuminate\Http\Request;

class PortfolioCategoryController extends BaseController
{
    public function __construct(protected PortfolioCategoryInterface $categoryRepository)
    {
    }

    /**
     * List portfolio categories
     *
     * @group Portfolio
     */
    public function index(Request $request)
    {
        $data = PortfolioCategory::query()
            ->wherePublished()
            ->orderByDesc('created_at')
            ->with(['slugable'])
            ->paginate($request->integer('per_page', 10) ?: 10);

        return $this
            ->httpResponse()
            ->setData(ListCategoryResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Filters portfolio categories
     *
     * @group Portfolio
     */
    public function getFilters(Request $request)
    {
        $filters = [
            'per_page' => $request->integer('per_page', 12),
            'order_by' => $request->input('order_by', 'created_at'),
            'order' => $request->input('order', 'ASC'),
        ];

        $data = $this->categoryRepository->getFilters($filters);

        return $this
            ->httpResponse()
            ->setData(CategoryResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Get portfolio category by slug
     *
     * @group Portfolio
     */
    public function findBySlug(string $slug)
    {
        $category = PortfolioCategory::query()
            ->wherePublished()
            ->whereHas('slugable', function ($query) use ($slug) {
                $query->where('key', $slug);
            })
            ->with(['children'])
            ->first();

        if (! $category) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('Not found');
        }

        return $this
            ->httpResponse()
            ->setData(new CategoryResource($category))
            ->toApiResponse();
    }
}
