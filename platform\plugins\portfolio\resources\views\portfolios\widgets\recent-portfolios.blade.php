<div class="scroller">
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>{{ trans('plugins/portfolio::portfolios.form.name') }}</th>
                    <th>{{ trans('core/base::tables.created_at') }}</th>
                    <th>{{ trans('core/base::tables.status') }}</th>
                </tr>
            </thead>
            <tbody>
                @forelse($portfolios as $portfolio)
                    <tr>
                        <td>
                            <a href="{{ route('portfolios.edit', $portfolio->id) }}">{{ $portfolio->name }}</a>
                        </td>
                        <td>{{ BaseHelper::formatDate($portfolio->created_at) }}</td>
                        <td>
                            {!! BaseHelper::clean($portfolio->status->toHtml()) !!}
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="3" class="text-center">{{ trans('core/dashboard::dashboard.no_data') }}</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
