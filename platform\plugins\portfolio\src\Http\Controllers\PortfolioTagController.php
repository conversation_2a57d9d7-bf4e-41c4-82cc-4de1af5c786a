<?php

namespace <PERSON><PERSON>qi\Portfolio\Http\Controllers;

use Shaqi\ACL\Models\User;
use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use Shaqi\Base\Events\UpdatedContentEvent;
use <PERSON>haqi\Base\Facades\PageTitle;
use Shaqi\Base\Http\Controllers\BaseController;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\Portfolio\Forms\PortfolioTagForm;
use Shaqi\Portfolio\Http\Requests\PortfolioTagRequest;
use Shaqi\Portfolio\Models\PortfolioTag;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioTagInterface;
use Shaqi\Portfolio\Tables\PortfolioTagTable;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PortfolioTagController extends BaseController
{
    public function __construct(protected PortfolioTagInterface $tagRepository)
    {
    }

    public function index(PortfolioTagTable $table)
    {
        PageTitle::setTitle(trans('plugins/portfolio::tags.menu'));

        return $table->renderTable();
    }

    public function create()
    {
        PageTitle::setTitle(trans('plugins/portfolio::tags.create'));

        return PortfolioTagForm::create()->renderForm();
    }

    public function store(PortfolioTagRequest $request): BaseHttpResponse
    {
        $form = PortfolioTagForm::create();

        $form
            ->saving(function (PortfolioTagForm $form) use ($request): void {
                $form
                    ->getModel()
                    ->fill([...$request->validated(), 'author_id' => Auth::guard()->id(), 'author_type' => User::class])
                    ->save();
            });

        event(new CreatedContentEvent(PORTFOLIO_TAG_MODULE_SCREEN_NAME, $request, $form->getModel()));

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('portfolio-tags.index'))
            ->setNextUrl(route('portfolio-tags.edit', $form->getModel()->getKey()))
            ->withCreatedSuccessMessage();
    }

    public function edit(PortfolioTag $tag)
    {
        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $tag->name]));

        return PortfolioTagForm::createFromModel($tag)->renderForm();
    }

    public function update(PortfolioTag $tag, PortfolioTagRequest $request): BaseHttpResponse
    {
        $form = PortfolioTagForm::createFromModel($tag);

        $form
            ->saving(function (PortfolioTagForm $form) use ($request): void {
                $form
                    ->getModel()
                    ->fill($request->validated())
                    ->save();
            });

        event(new UpdatedContentEvent(PORTFOLIO_TAG_MODULE_SCREEN_NAME, $request, $tag));

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('portfolio-tags.index'))
            ->withUpdatedSuccessMessage();
    }

    public function destroy(PortfolioTag $tag): BaseHttpResponse
    {
        try {
            $tag->delete();

            event(new DeletedContentEvent(PORTFOLIO_TAG_MODULE_SCREEN_NAME, request(), $tag));

            return $this
                ->httpResponse()
                ->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function getAllTags(): BaseHttpResponse
    {
        return $this
            ->httpResponse()
            ->setData($this->tagRepository->getAllTags());
    }
}
