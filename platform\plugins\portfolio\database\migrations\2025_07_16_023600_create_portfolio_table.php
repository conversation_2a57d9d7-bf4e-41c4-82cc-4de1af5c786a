<?php

use <PERSON><PERSON><PERSON>\ACL\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('portfolio_categories', function (Blueprint $table): void {
            $table->id();
            $table->string('name', 120);
            $table->foreignId('parent_id')->default(0);
            $table->string('description', 400)->nullable();
            $table->string('status', 60)->default('published');
            $table->foreignId('author_id');
            $table->string('author_type')->default(addslashes(User::class));
            $table->string('icon', 60)->nullable();
            $table->tinyInteger('order')->default(0);
            $table->tinyInteger('is_featured')->default(0);
            $table->tinyInteger('is_default')->unsigned()->default(0);
            $table->timestamps();
        });

        Schema::create('portfolio_tags_table', function (Blueprint $table): void {
            $table->id();
            $table->string('name', 120);
            $table->foreignId('author_id');
            $table->string('author_type')->default(addslashes(User::class));
            $table->string('description', 400)->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        Schema::create('portfolios', function (Blueprint $table): void {
            $table->id();
            $table->string('name');
            $table->string('description', 400)->nullable();
            $table->longText('content')->nullable();
            $table->string('status', 60)->default('published');
            $table->foreignId('author_id');
            $table->string('author_type')->default(addslashes(User::class));
            $table->tinyInteger('is_featured')->unsigned()->default(0);
            $table->string('image')->nullable();
            $table->string('logo')->nullable();
            $table->string('website_link')->nullable();
            $table->json('features')->nullable();
            $table->json('gallery')->nullable();
            $table->integer('views')->unsigned()->default(0);
            $table->timestamps();
        });

        Schema::create('portfolio_tags', function (Blueprint $table): void {
            $table->foreignId('tag_id')->index();
            $table->foreignId('portfolio_id')->index();
        });

        Schema::create('portfolio_category_pivot', function (Blueprint $table): void {
            $table->foreignId('category_id')->index();
            $table->foreignId('portfolio_id')->index();
        });
    }

    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('portfolio_tags');
        Schema::dropIfExists('portfolio_category_pivot');
        Schema::dropIfExists('portfolios');
        Schema::dropIfExists('portfolio_categories');
        Schema::dropIfExists('portfolio_tags_table');
    }
};
