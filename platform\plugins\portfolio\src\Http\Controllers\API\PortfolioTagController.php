<?php

namespace <PERSON>haqi\Portfolio\Http\Controllers\API;

use Shaqi\Api\Http\Controllers\BaseController;
use <PERSON>haqi\Portfolio\Http\Resources\TagResource;
use <PERSON>haqi\Portfolio\Models\PortfolioTag;
use <PERSON>haqi\Portfolio\Repositories\Interfaces\PortfolioTagInterface;
use Illuminate\Http\Request;

class PortfolioTagController extends BaseController
{
    public function __construct(protected PortfolioTagInterface $tagRepository)
    {
    }

    /**
     * List portfolio tags
     *
     * @group Portfolio
     */
    public function index(Request $request)
    {
        $data = PortfolioTag::query()
            ->wherePublished()
            ->orderByDesc('created_at')
            ->with(['slugable'])
            ->paginate($request->integer('per_page', 10) ?: 10);

        return $this
            ->httpResponse()
            ->setData(TagResource::collection($data))
            ->toApiResponse();
    }
}
