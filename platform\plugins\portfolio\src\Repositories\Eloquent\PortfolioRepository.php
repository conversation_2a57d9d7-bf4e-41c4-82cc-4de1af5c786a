<?php

namespace <PERSON><PERSON>qi\Portfolio\Repositories\Eloquent;

use Shaqi\Base\Models\BaseQueryBuilder;
use Shaqi\Portfolio\Models\Portfolio;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioInterface;
use Shaqi\Language\Facades\Language;
use Shaqi\Support\Repositories\Eloquent\RepositoriesAbstract;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class PortfolioRepository extends RepositoriesAbstract implements PortfolioInterface
{
    public function getFeatured(int $limit = 5, array $with = []): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->where('is_featured', true)
            ->limit($limit)
            ->with(array_merge(['slugable'], $with))
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getListPortfolioNonInList(array $selected = [], int $limit = 7, array $with = []): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->whereNotIn('id', $selected)
            ->limit($limit)
            ->with($with)
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getRelated(int|string $id, int $limit = 3): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->where('id', '!=', $id)
            ->limit($limit)
            ->with(['slugable'])
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getRelatedCategoryIds(Portfolio|int|string $model): array
    {
        if (is_numeric($model)) {
            $model = $this->findById($model);
        }

        if (! $model instanceof Portfolio) {
            return [];
        }

        return $model->categories()->allRelatedIds()->toArray();
    }

    public function getByCategory(
        array|int|string $categoryId,
        int $paginate = 12,
        int $limit = 0
    ): Collection|LengthAwarePaginator {
        $data = $this->model
            ->wherePublished()
            ->whereHas('categories', function (Builder $query) use ($categoryId): void {
                $query->whereIn('portfolio_categories.id', array_filter((array) $categoryId));
            })
            ->select('*')
            ->distinct()
            ->with('slugable')
            ->orderByDesc('created_at');

        if ($paginate != 0) {
            return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
        }

        return $this->applyBeforeExecuteQuery($data)->limit($limit)->get();
    }

    public function getByUserId(int|string $authorId, int $paginate = 6): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->wherePublished()
            ->where('author_id', $authorId)
            ->orderByDesc('created_at')
            ->with(['slugable']);

        if ($paginate != 0) {
            return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getDataSiteMap(): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->with('slugable')
            ->wherePublished()
            ->orderByDesc('created_at')
            ->select(['id', 'name', 'updated_at']);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getByTag(int|string $tag, int $paginate = 12): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->wherePublished()
            ->whereHas('tags', function (Builder $query) use ($tag): void {
                $query->where('portfolio_tags_table.id', $tag);
            })
            ->select('*')
            ->distinct()
            ->with('slugable')
            ->orderByDesc('created_at');

        if ($paginate != 0) {
            return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getRecentPortfolios(int $limit = 5, int|string $categoryId = 0): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->limit($limit)
            ->with(['slugable'])
            ->orderByDesc('created_at');

        if ($categoryId != 0) {
            $data = $data->whereHas('categories', function (Builder $query) use ($categoryId): void {
                $query->where('portfolio_categories.id', $categoryId);
            });
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getSearch(?string $keyword, int $limit = 10, int $paginate = 10): Collection|LengthAwarePaginator
    {
        $data = $this->model->wherePublished();

        if ($keyword) {
            $data = $data->where(function (Builder $query) use ($keyword): void {
                $query
                    ->where('name', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('description', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('content', 'LIKE', '%' . $keyword . '%');
            });
        }

        $data = $data
            ->with(['slugable'])
            ->orderByDesc('created_at');

        if ($paginate != 0) {
            return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
        }

        return $this->applyBeforeExecuteQuery($data)->limit($limit)->get();
    }

    public function getAllPortfolios(int $perPage = 12, bool $active = true, array $with = ['slugable']): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->with($with)
            ->orderByDesc('created_at');

        if ($active) {
            $data = $data->wherePublished();
        }

        if ($perPage != 0) {
            return $this->applyBeforeExecuteQuery($data)->paginate($perPage);
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getPopularPortfolios(int $limit, array $args = []): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->orderBy('views', 'DESC')
            ->limit($limit)
            ->with(['slugable']);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getFilters(array $filters): Collection|LengthAwarePaginator
    {
        $orderBy = $filters['order_by'] ?? 'created_at';
        $order = $filters['order'] ?? 'DESC';

        $data = $this->model->wherePublished()->orderBy($orderBy, $order);

        return $this->applyBeforeExecuteQuery($data)->paginate((int) $filters['per_page']);
    }
}
