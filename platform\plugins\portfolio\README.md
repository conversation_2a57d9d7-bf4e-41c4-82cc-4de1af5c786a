# Portfolio Plugin

A comprehensive portfolio management system for the Shaqi CMS, built following the same patterns as the blog plugin.

## Features

### Main Components
- **Portfolios**: Main content type for portfolio items
- **Portfolio Categories**: Hierarchical categorization system
- **Portfolio Tags**: Tagging system for better organization

### Portfolio Fields
- **Name**: Portfolio title
- **Category**: Taxonomy relationship to categories
- **Tags**: Taxonomy relationship to tags
- **Description**: Short description/excerpt
- **Content**: Rich text editor for detailed description
- **Image**: Featured image upload
- **Website Link**: URL field for live project link
- **Logo**: Image media for project logo
- **Features**: Repeater field for project features (e.g., "WordPress Website", "Website Hosting", etc.)
- **Gallery**: Multiple image upload field for project screenshots

### Single Page Templates
- Single portfolio item page
- Single portfolio category page
- Single portfolio tag page

## Installation

1. The plugin is already created in `platform/plugins/portfolio/`
2. Run migrations to create the database tables:
   ```bash
   php artisan migrate
   ```
3. Clear cache:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   ```

## Usage

### Admin Interface
- Navigate to **Portfolio** in the admin menu
- Manage portfolios, categories, and tags
- Create new portfolio items with all the required fields
- Organize portfolios using categories and tags

### Frontend Display
- Portfolio items are accessible via their individual URLs
- Category and tag pages show related portfolios
- Use helper functions in themes to display portfolios

### Helper Functions
```php
// Get featured portfolios
get_featured_portfolios($limit = 5)

// Get recent portfolios
get_recent_portfolios($limit = 5, $categoryId = 0)

// Get related portfolios
get_related_portfolios($portfolioId, $limit = 3)

// Get portfolios by category
get_portfolios_by_category($categoryId, $paginate = 12)

// Get portfolios by tag
get_portfolios_by_tag($tagId, $paginate = 12)

// Get all portfolios
get_all_portfolios($perPage = 12, $active = true)
```

### API Endpoints
- `GET /api/v1/portfolios` - List portfolios
- `GET /api/v1/portfolios/{slug}` - Get portfolio by slug
- `GET /api/v1/portfolio-categories` - List categories
- `GET /api/v1/portfolio-tags` - List tags
- `GET /api/v1/search?q=keyword` - Search portfolios

## Database Structure

### Tables Created
- `portfolios` - Main portfolio items
- `portfolio_categories` - Portfolio categories
- `portfolio_tags_table` - Portfolio tags
- `portfolio_tags` - Portfolio-tag pivot table
- `portfolio_category_pivot` - Portfolio-category pivot table

## Permissions
- `portfolios.index` - View portfolios list
- `portfolios.create` - Create new portfolios
- `portfolios.edit` - Edit portfolios
- `portfolios.destroy` - Delete portfolios
- `portfolio-categories.*` - Category management permissions
- `portfolio-tags.*` - Tag management permissions

## Theme Integration
The plugin provides theme templates that can be customized:
- `plugins/portfolio::themes.portfolio` - Single portfolio page
- `plugins/portfolio::themes.category` - Category page
- `plugins/portfolio::themes.tag` - Tag page

## Shortcode
Use `[portfolios limit="6"]` shortcode to display portfolios in pages/posts.

## Technical Details
- Built following the same architecture as the blog plugin
- Uses repository pattern for data access
- Includes form builders for admin interface
- Supports SEO meta tags and schema markup
- Includes API resources for frontend integration
- Supports multilingual content (if language modules are enabled)
