<?php

namespace <PERSON>haqi\Portfolio\Repositories\Interfaces;

use Shaqi\Support\Repositories\Interfaces\RepositoryInterface;
use Illuminate\Support\Collection;

interface PortfolioTagInterface extends RepositoryInterface
{
    public function getDataSiteMap(): Collection;

    public function getPopularTags(int $limit, array $with = ['slugable'], array $withCount = ['portfolios']): Collection;

    public function getAllTags(bool $active = true): Collection;
}
