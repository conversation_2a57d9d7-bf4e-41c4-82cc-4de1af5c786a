<?php

namespace Shaqi\Portfolio\Http\Requests;

use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PortfolioRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:400',
            'content' => 'nullable|string',
            'image' => 'nullable|string',
            'logo' => 'nullable|string',
            'website_link' => 'nullable|url|max:255',
            'features' => 'nullable|array',
            'features.*' => 'nullable|string|max:255',
            'gallery' => 'nullable|array',
            'gallery.*' => 'nullable|string',
            'is_featured' => 'nullable|boolean',
            'status' => Rule::in(BaseStatusEnum::values()),
            'categories' => 'nullable|array',
            'categories.*' => 'nullable|integer|exists:portfolio_categories,id',
            'tag' => 'nullable|string',
        ];
    }
}
