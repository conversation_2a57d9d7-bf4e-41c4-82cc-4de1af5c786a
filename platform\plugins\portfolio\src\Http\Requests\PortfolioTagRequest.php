<?php

namespace <PERSON>haqi\Portfolio\Http\Requests;

use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PortfolioTagRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:120',
            'description' => 'nullable|string|max:400',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }
}
