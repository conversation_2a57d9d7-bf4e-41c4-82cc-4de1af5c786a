<?php

namespace <PERSON><PERSON>qi\Portfolio\Services;

use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Facades\AdminHelper;
use Shaqi\Base\Supports\Helper;
use <PERSON>haqi\Portfolio\Models\Portfolio;
use <PERSON>haqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Models\PortfolioTag;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioInterface;
use Shaqi\Media\Facades\RvMedia;
use <PERSON>haqi\SeoHelper\Facades\SeoHelper;
use Shaqi\SeoHelper\SeoOpenGraph;
use Shaqi\Slug\Models\Slug;
use Shaqi\Theme\Facades\AdminBar;
use Shaqi\Theme\Facades\Theme;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;

class PortfolioService
{
    public function handleFrontRoutes(Slug|array $slug): Slug|array
    {
        if (! $slug instanceof Slug) {
            return $slug;
        }

        $condition = [
            'id' => $slug->reference_id,
            'status' => BaseStatusEnum::PUBLISHED,
        ];

        switch ($slug->reference_type) {
            case Portfolio::class:
                $portfolio = Portfolio::query()
                    ->where($condition)
                    ->with(['categories', 'tags', 'author'])
                    ->first();

                if (empty($portfolio)) {
                    abort(404);
                }

                SeoHelper::setTitle($portfolio->name)
                    ->setDescription($portfolio->description);

                $meta = new SeoOpenGraph();
                if ($portfolio->image) {
                    $meta->setImage(RvMedia::getImageUrl($portfolio->image));
                }
                $meta->setDescription($portfolio->description);
                $meta->setUrl($portfolio->url);
                $meta->setTitle($portfolio->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                if (function_exists('admin_bar')) {
                    AdminBar::setIsDisplay(true)
                        ->setLinksNofollow(false)
                        ->setMenuItemTemplate('edit-portfolio')
                        ->setMenuItemTitle(trans('plugins/portfolio::portfolios.edit_this_portfolio'))
                        ->setMenuItemIcon('fas fa-edit')
                        ->setMenuItemRoute(route('portfolios.edit', $portfolio->id))
                        ->setMenuItemPermission('portfolios.edit');
                }

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    ->add(__('Portfolio'), route('public.portfolio'))
                    ->add($portfolio->name, $portfolio->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PORTFOLIO_MODULE_SCREEN_NAME, $portfolio);

                return [
                    'view' => 'plugins/portfolio::themes.portfolio',
                    'data' => compact('portfolio'),
                ];

            case PortfolioCategory::class:
                $category = PortfolioCategory::query()
                    ->where($condition)
                    ->with(['children'])
                    ->first();

                if (empty($category)) {
                    abort(404);
                }

                SeoHelper::setTitle($category->name)
                    ->setDescription($category->description);

                $allRelatedCategoryIds = [$category->id];
                $allRelatedCategoryIds = array_merge(
                    $allRelatedCategoryIds,
                    app(PortfolioCategoryInterface::class)->getAllRelatedChildrenIds($category)
                );

                $portfolios = app(PortfolioInterface::class)->getByCategory($allRelatedCategoryIds, 12);

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    ->add(__('Portfolio'), route('public.portfolio'))
                    ->add($category->name, $category->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PORTFOLIO_CATEGORY_MODULE_SCREEN_NAME, $category);

                return [
                    'view' => 'plugins/portfolio::themes.category',
                    'data' => compact('category', 'portfolios'),
                ];

            case PortfolioTag::class:
                $tag = PortfolioTag::query()
                    ->where($condition)
                    ->first();

                if (empty($tag)) {
                    abort(404);
                }

                SeoHelper::setTitle($tag->name)
                    ->setDescription($tag->description);

                $portfolios = app(PortfolioInterface::class)->getByTag($tag->id, 12);

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    ->add(__('Portfolio'), route('public.portfolio'))
                    ->add($tag->name, $tag->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PORTFOLIO_TAG_MODULE_SCREEN_NAME, $tag);

                return [
                    'view' => 'plugins/portfolio::themes.tag',
                    'data' => compact('tag', 'portfolios'),
                ];
        }

        return $slug;
    }

    public function getSiteMap(): string
    {
        $portfolios = app(PortfolioInterface::class)->getDataSiteMap();

        $siteMap = '';
        foreach ($portfolios as $portfolio) {
            $siteMap .= SiteMapManager::getSiteMapRoute($portfolio->url, $portfolio->updated_at);
        }

        $categories = app(PortfolioCategoryInterface::class)->getDataSiteMap();

        foreach ($categories as $category) {
            $siteMap .= SiteMapManager::getSiteMapRoute($category->url, $category->updated_at);
        }

        $tags = app(PortfolioTagInterface::class)->getDataSiteMap();

        foreach ($tags as $tag) {
            $siteMap .= SiteMapManager::getSiteMapRoute($tag->url, $tag->updated_at);
        }

        return $siteMap;
    }
}
