<?php

namespace <PERSON><PERSON><PERSON>\Portfolio\Listeners;

use <PERSON><PERSON><PERSON>\Base\Events\CreatedContentEvent;
use Exception;

class CreatedContentListener
{
    public function handle(CreatedContentEvent $event): void
    {
        try {
            do_action(BASE_ACTION_AFTER_CREATE_CONTENT, $event->screen, $event->request, $event->data);
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}
