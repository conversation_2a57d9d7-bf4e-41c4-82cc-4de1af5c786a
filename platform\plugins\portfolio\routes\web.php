<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use <PERSON>haqi\Portfolio\Http\Controllers\ExportPortfolioController;
use <PERSON>ha<PERSON>\Portfolio\Http\Controllers\ImportPortfolioController;
use <PERSON>haqi\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Shaqi\Portfolio\Http\Controllers'], function (): void {
    AdminHelper::registerRoutes(function (): void {
        Route::group(['prefix' => 'portfolio'], function (): void {
            Route::group(['prefix' => 'portfolios', 'as' => 'portfolios.'], function (): void {
                Route::resource('', 'PortfolioController')
                    ->parameters(['' => 'portfolio']);

                Route::get('widgets/recent-portfolios', [
                    'as' => 'widget.recent-portfolios',
                    'uses' => 'PortfolioController@getWidgetRecentPortfolios',
                    'permission' => 'portfolios.index',
                ]);
            });

            Route::group(['prefix' => 'categories', 'as' => 'portfolio-categories.'], function (): void {
                Route::resource('', 'PortfolioCategoryController')
                    ->parameters(['' => 'category']);
            });

            Route::group(['prefix' => 'tags', 'as' => 'portfolio-tags.'], function (): void {
                Route::resource('', 'PortfolioTagController')
                    ->parameters(['' => 'tag']);

                Route::get('all', [
                    'as' => 'all',
                    'uses' => 'PortfolioTagController@getAllTags',
                    'permission' => 'portfolio-tags.index',
                ]);
            });
        });

        Route::group(['prefix' => 'tools/data-synchronize', 'permission' => 'tools.data-synchronize'], function (): void {
            Route::group(['prefix' => 'export', 'as' => 'tools.data-synchronize.export.', 'permission' => 'portfolios.export'], function (): void {
                Route::group(['prefix' => 'portfolios', 'as' => 'portfolios.'], function (): void {
                    Route::get('/', [ExportPortfolioController::class, 'index'])->name('index');
                    Route::post('/', [ExportPortfolioController::class, 'store'])->name('store');
                });
            });

            Route::group(['prefix' => 'import', 'as' => 'tools.data-synchronize.import.', 'permission' => 'portfolios.import'], function (): void {
                Route::group(['prefix' => 'portfolios', 'as' => 'portfolios.'], function (): void {
                    Route::get('/', [ImportPortfolioController::class, 'index'])->name('index');
                    Route::post('/', [ImportPortfolioController::class, 'store'])->name('store');
                    Route::post('validate', [ImportPortfolioController::class, 'validateData'])->name('validate');
                    Route::post('download-example', [ImportPortfolioController::class, 'downloadExample'])->name('download-example');
                });
            });
        });

        Route::group(['prefix' => 'settings', 'namespace' => 'Settings'], function (): void {
            Route::get('portfolio', [
                'as' => 'portfolio.settings',
                'uses' => 'PortfolioSettingController@edit',
            ]);

            Route::put('portfolio', [
                'as' => 'portfolio.settings.update',
                'uses' => 'PortfolioSettingController@update',
                'permission' => 'portfolio.settings',
            ]);
        });
    });
});

if (defined('THEME_MODULE_SCREEN_NAME')) {
    Theme::registerRoutes(function (): void {
        Route::get('portfolio', 'PortfolioController@getPortfolios')
            ->name('public.portfolio');
    });
}
