<div class="portfolios-widget">
    @if ($portfolios->isNotEmpty())
        <div class="portfolio-list">
            @foreach ($portfolios as $portfolio)
                <article class="portfolio-item">
                    @if ($portfolio->image)
                        <div class="portfolio-image">
                            <a href="{{ $portfolio->url }}">
                                <img src="{{ RvMedia::getImageUrl($portfolio->image) }}" alt="{{ $portfolio->name }}" class="img-fluid">
                            </a>
                        </div>
                    @endif
                    
                    <div class="portfolio-content">
                        <h4><a href="{{ $portfolio->url }}">{{ $portfolio->name }}</a></h4>
                        
                        @if ($portfolio->description)
                            <p>{{ Str::limit($portfolio->description, 100) }}</p>
                        @endif
                        
                        @if ($portfolio->website_link)
                            <a href="{{ $portfolio->website_link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                {{ __('Visit Website') }}
                            </a>
                        @endif
                        
                        <div class="portfolio-meta">
                            <span>{{ $portfolio->created_at->format('M d, Y') }}</span>
                            
                            @if ($portfolio->categories->isNotEmpty())
                                <span class="category">{{ $portfolio->categories->first()->name }}</span>
                            @endif
                        </div>
                    </div>
                </article>
            @endforeach
        </div>
    @endif
</div>
