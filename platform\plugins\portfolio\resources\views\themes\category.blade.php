<div>
    <h3>{{ $category->name }}</h3>
    {!! Theme::breadcrumb()->render() !!}
</div>

<header>
    <h3>{{ $category->name }}</h3>
    @if ($category->description)
        <p>{{ $category->description }}</p>
    @endif
</header>

@if ($portfolios->isNotEmpty())
    <div class="portfolio-list">
        @foreach ($portfolios as $portfolio)
            <article class="portfolio-item">
                @if ($portfolio->image)
                    <div class="portfolio-image">
                        <a href="{{ $portfolio->url }}">
                            <img src="{{ RvMedia::getImageUrl($portfolio->image) }}" alt="{{ $portfolio->name }}" class="img-fluid">
                        </a>
                    </div>
                @endif
                
                <div class="portfolio-content">
                    <h4><a href="{{ $portfolio->url }}">{{ $portfolio->name }}</a></h4>
                    
                    @if ($portfolio->description)
                        <p>{{ $portfolio->description }}</p>
                    @endif
                    
                    @if ($portfolio->website_link)
                        <a href="{{ $portfolio->website_link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            {{ __('Visit Website') }}
                        </a>
                    @endif
                    
                    <div class="portfolio-meta">
                        <span>{{ $portfolio->created_at->format('M d, Y') }}</span>
                        
                        @if ($portfolio->tags->isNotEmpty())
                            <div class="portfolio-tags">
                                @foreach ($portfolio->tags as $tag)
                                    <a href="{{ $tag->url }}" class="tag">{{ $tag->name }}</a>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </article>
        @endforeach
    </div>
    
    @if ($portfolios instanceof \Illuminate\Pagination\LengthAwarePaginator)
        {!! $portfolios->links() !!}
    @endif
@else
    <div class="alert alert-info">
        {{ __('No portfolios found in this category.') }}
    </div>
@endif
