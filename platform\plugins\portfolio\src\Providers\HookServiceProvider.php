<?php

namespace <PERSON><PERSON>qi\Portfolio\Providers;

use Shaqi\Base\Facades\Assets;
use Shaqi\Base\Facades\BaseHelper;
use <PERSON>haqi\Base\Facades\Html;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use <PERSON>haqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Supports\ServiceProvider;
use Shaqi\Portfolio\Models\Portfolio;
use Shaqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Models\PortfolioTag;
use Shaqi\Portfolio\Services\PortfolioService;
use Shaqi\Dashboard\Events\RenderingDashboardWidgets;
use Shaqi\Dashboard\Supports\DashboardWidgetInstance;
use Shaqi\Media\Facades\RvMedia;
use Shaqi\Menu\Events\RenderingMenuOptions;
use Shaqi\Menu\Facades\Menu;
use Shaqi\Page\Models\Page;
use Shaqi\Page\Tables\PageTable;
use Shaqi\Shortcode\Compilers\Shortcode;
use <PERSON><PERSON>qi\Shortcode\Facades\Shortcode as ShortcodeFacade;
use <PERSON><PERSON>qi\Slug\Models\Slug;
use Shaqi\Theme\Events\RenderingSingleEvent;
use Shaqi\Theme\Events\RenderingSiteMapEvent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Menu::addMenuOptionModel(PortfolioCategory::class);
        Menu::addMenuOptionModel(PortfolioTag::class);

        $this->app['events']->listen(RenderingMenuOptions::class, function (): void {
            add_action(MENU_ACTION_SIDEBAR_OPTIONS, [$this, 'registerMenuOptions'], 2);
        });

        $this->app['events']->listen(RenderingDashboardWidgets::class, function (): void {
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [$this, 'registerDashboardWidgets'], 21, 2);
        });

        add_filter(BASE_FILTER_PUBLIC_SINGLE_DATA, [$this, 'handleSingleView'], 2);

        if (defined('PAGE_MODULE_SCREEN_NAME')) {
            add_filter(PAGE_FILTER_FRONT_PAGE_CONTENT, [$this, 'renderPortfolioPage'], 2, 2);
        }

        $this->app['events']->listen(RenderingSiteMapEvent::class, function (): void {
            echo (new PortfolioService())->getSiteMap();
        });

        if (function_exists('shortcode')) {
            add_shortcode('portfolios', trans('plugins/portfolio::base.shortcode_name'), trans('plugins/portfolio::base.shortcode_description'), [$this, 'render']);
            shortcode()->setAdminConfig('portfolios', view('plugins/portfolio::partials.portfolios-short-code-admin-config')->render());
        }

        if (defined('PAGE_MODULE_SCREEN_NAME')) {
            PageTable::beforeRendering(function (): void {
                add_filter(PAGE_FILTER_PAGE_NAME_IN_ADMIN_LIST, [$this, 'addAdditionNameToPageName'], 147, 2);
            });
        }

        $this->app['events']->listen(RenderingSingleEvent::class, function (): void {
            add_filter(THEME_FRONT_HEADER, [$this, 'addSchemaJsonLd'], 2);
        });
    }

    public function addSchemaJsonLd(?string $html): ?string
    {
        $schema = null;

        if (get_class(Theme::getThemeNamespace()) === Portfolio::class) {
            $portfolio = Theme::get('portfolio');
            if ($portfolio) {
                $schema = [
                    '@context' => 'https://schema.org',
                    '@type' => 'CreativeWork',
                    'name' => $portfolio->name,
                    'description' => $portfolio->description,
                    'url' => $portfolio->url,
                    'image' => RvMedia::getImageUrl($portfolio->image),
                    'dateCreated' => $portfolio->created_at->toISOString(),
                    'dateModified' => $portfolio->updated_at->toISOString(),
                    'author' => [
                        '@type' => 'Organization',
                        'name' => theme_option('site_title'),
                    ],
                ];

                if ($portfolio->categories->isNotEmpty()) {
                    $schema['genre'] = $portfolio->categories->pluck('name')->toArray();
                }

                if ($portfolio->tags->isNotEmpty()) {
                    $schema['keywords'] = $portfolio->tags->pluck('name')->implode(', ');
                }
            }
        }

        if ($schema) {
            $html .= Html::tag('script', json_encode($schema), ['type' => 'application/ld+json'])->toHtml();
        }

        return $html;
    }

    public function render(Shortcode $shortcode): ?string
    {
        $limit = (int) $shortcode->limit ?: 6;

        $portfolios = get_recent_portfolios($limit);

        if ($portfolios->isEmpty()) {
            return null;
        }

        return view('plugins/portfolio::themes.templates.portfolios', compact('portfolios'))->render();
    }

    public function registerMenuOptions(): void
    {
        if (Auth::guard()->user()->hasPermission('portfolios.index')) {
            Menu::addMenuOptionModel(Portfolio::class);
            add_action(MENU_ACTION_SIDEBAR_OPTIONS, [$this, 'addPortfolioMenuOptions'], 13);
        }
    }

    public function addPortfolioMenuOptions(): void
    {
        echo view('plugins/portfolio::partials.menu-options')->render();
    }

    public function registerDashboardWidgets(array $widgets, Collection $widgetSettings): array
    {
        if (! Auth::guard()->user()->hasPermission('portfolios.index')) {
            return $widgets;
        }

        Assets::addScriptsDirectly(['/vendor/core/plugins/portfolio/js/portfolio.js']);

        return (new DashboardWidgetInstance())
            ->setPermission('portfolios.index')
            ->setKey('widget_portfolios_recent')
            ->setTitle(trans('plugins/portfolio::portfolios.widget_portfolios_recent'))
            ->setIcon('fas fa-briefcase')
            ->setColor('yellow')
            ->setRoute(route('portfolios.widget.recent-portfolios'))
            ->setBodyClass('')
            ->setColumn('col-md-6 col-sm-6')
            ->init($widgets, $widgetSettings);
    }

    public function handleSingleView(Slug|array $slug): Slug|array
    {
        return (new PortfolioService())->handleFrontRoutes($slug);
    }

    public function renderPortfolioPage(?string $html, Page $page): ?string
    {
        if ($page->template !== 'portfolio') {
            return $html;
        }

        $view = view('plugins/portfolio::themes.portfolio', compact('page'));

        if ($view->exists()) {
            return $view->render();
        }

        return $html;
    }

    public function addAdditionNameToPageName(?string $name, Page $page): ?string
    {
        if ($page->template === 'portfolio') {
            $subTitle = Html::tag('span', trans('plugins/portfolio::base.portfolio_page'), [
                'class' => 'additional-page-name',
                'style' => 'font-size: 13px;color: #666;font-weight: 400;margin-left: 10px;',
            ]);

            return $name . $subTitle;
        }

        return $name;
    }
}
