<?php

namespace <PERSON><PERSON>qi\Portfolio\Tables;

use Shaqi\Base\Facades\Html;
use Shaqi\Base\Models\BaseQueryBuilder;
use <PERSON>haqi\Portfolio\Models\Portfolio;
use <PERSON>haqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Table\Abstracts\TableAbstract;
use Shaqi\Table\Actions\DeleteAction;
use Shaqi\Table\Actions\EditAction;
use Shaqi\Table\BulkActions\DeleteBulkAction;
use Shaqi\Table\BulkChanges\CreatedAtBulkChange;
use Shaqi\Table\BulkChanges\NameBulkChange;
use Shaqi\Table\BulkChanges\SelectBulkChange;
use Shaqi\Table\BulkChanges\StatusBulkChange;
use Shaqi\Table\Columns\CreatedAtColumn;
use Shaqi\Table\Columns\FormattedColumn;
use Shaqi\Table\Columns\IdColumn;
use Shaqi\Table\Columns\ImageColumn;
use Shaqi\Table\Columns\NameColumn;
use Shaqi\Table\Columns\StatusColumn;
use Shaqi\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\Request;

class PortfolioTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Portfolio::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('portfolios.create'))
            ->addColumns([
                IdColumn::make(),
                ImageColumn::make(),
                NameColumn::make()->route('portfolios.edit'),
                FormattedColumn::make('categories')
                    ->title(trans('plugins/portfolio::portfolios.categories'))
                    ->orderable(false)
                    ->searchable(false)
                    ->getValueUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        if (! $item->categories->count()) {
                            return '&mdash;';
                        }

                        return Html::tag('span', $item->categories->pluck('name')->implode(', '), [
                            'class' => 'label-success status-label',
                        ])->toHtml();
                    }),
                FormattedColumn::make('website_link')
                    ->title(trans('plugins/portfolio::portfolios.website_link'))
                    ->orderable(false)
                    ->searchable(false)
                    ->getValueUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        if (! $item->website_link) {
                            return '&mdash;';
                        }

                        return Html::link($item->website_link, $item->website_link, [
                            'target' => '_blank',
                            'class' => 'text-primary',
                        ])->toHtml();
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('portfolios.edit'),
                DeleteAction::make()->route('portfolios.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('portfolios.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
                SelectBulkChange::make()
                    ->name('category')
                    ->title(trans('plugins/portfolio::portfolios.categories'))
                    ->searchable()
                    ->choices(fn () => PortfolioCategory::query()->wherePublished()->pluck('name', 'id')->all()),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'image',
                        'website_link',
                        'created_at',
                        'status',
                        'is_featured',
                    ])
                    ->with(['categories']);
            })
            ->onFilterQuery(function (Builder|QueryBuilder|Relation $query, Request $request) {
                $keyword = $request->input('search.value');
                if ($keyword) {
                    return $query
                        ->where(function (Builder $query) use ($keyword): void {
                            $query
                                ->where('portfolios.name', 'LIKE', '%' . $keyword . '%')
                                ->orWhere('portfolios.description', 'LIKE', '%' . $keyword . '%');
                        });
                }

                return $query;
            });
    }
}
