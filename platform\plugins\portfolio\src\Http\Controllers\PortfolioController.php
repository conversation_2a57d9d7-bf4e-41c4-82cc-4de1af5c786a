<?php

namespace <PERSON><PERSON>qi\Portfolio\Http\Controllers;

use Shaqi\ACL\Models\User;
use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use Shaqi\Base\Events\UpdatedContentEvent;
use Shaqi\Base\Facades\PageTitle;
use Shaqi\Base\Forms\FormBuilder;
use Shaqi\Base\Http\Controllers\BaseController;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\Portfolio\Forms\PortfolioForm;
use Shaqi\Portfolio\Http\Requests\PortfolioRequest;
use Shaqi\Portfolio\Models\Portfolio;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioInterface;
use Shaqi\Portfolio\Tables\PortfolioTable;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PortfolioController extends BaseController
{
    public function __construct(protected PortfolioInterface $portfolioRepository)
    {
    }

    public function index(PortfolioTable $table)
    {
        PageTitle::setTitle(trans('plugins/portfolio::portfolios.menu'));

        return $table->renderTable();
    }

    public function create()
    {
        PageTitle::setTitle(trans('plugins/portfolio::portfolios.create'));

        return PortfolioForm::create()->renderForm();
    }

    public function store(PortfolioRequest $request): BaseHttpResponse
    {
        $form = PortfolioForm::create();

        $form
            ->saving(function (PortfolioForm $form) use ($request): void {
                $form
                    ->getModel()
                    ->fill([...$request->validated(), 'author_id' => Auth::guard()->id(), 'author_type' => User::class])
                    ->save();

                $this->saveCategories($form->getModel(), $request);
                $this->saveTags($form->getModel(), $request);
            });

        event(new CreatedContentEvent(PORTFOLIO_MODULE_SCREEN_NAME, $request, $form->getModel()));

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('portfolios.index'))
            ->setNextUrl(route('portfolios.edit', $form->getModel()->getKey()))
            ->withCreatedSuccessMessage();
    }

    public function show(Portfolio $portfolio)
    {
        PageTitle::setTitle($portfolio->name);

        return view('plugins/portfolio::portfolios.show', compact('portfolio'));
    }

    public function edit(Portfolio $portfolio, FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $portfolio->name]));

        return PortfolioForm::createFromModel($portfolio)->renderForm();
    }

    public function update(Portfolio $portfolio, PortfolioRequest $request): BaseHttpResponse
    {
        $form = PortfolioForm::createFromModel($portfolio);

        $form
            ->saving(function (PortfolioForm $form) use ($request): void {
                $form
                    ->getModel()
                    ->fill($request->validated())
                    ->save();

                $this->saveCategories($form->getModel(), $request);
                $this->saveTags($form->getModel(), $request);
            });

        event(new UpdatedContentEvent(PORTFOLIO_MODULE_SCREEN_NAME, $request, $form->getModel()));

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('portfolios.index'))
            ->withUpdatedSuccessMessage();
    }

    public function destroy(Portfolio $portfolio): BaseHttpResponse
    {
        try {
            $portfolio->delete();

            event(new DeletedContentEvent(PORTFOLIO_MODULE_SCREEN_NAME, request(), $portfolio));

            return $this
                ->httpResponse()
                ->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    protected function saveCategories(Portfolio $portfolio, Request $request): void
    {
        $categories = $request->input('categories', []);
        $portfolio->categories()->sync($categories);
    }

    protected function saveTags(Portfolio $portfolio, Request $request): void
    {
        $tagsInput = $request->input('tag');
        if (! $tagsInput) {
            $portfolio->tags()->detach();
            return;
        }

        $tags = collect(explode(',', $tagsInput))
            ->map(fn ($tag) => trim($tag))
            ->filter()
            ->unique()
            ->map(function ($tagName) {
                return \Shaqi\Portfolio\Models\PortfolioTag::firstOrCreate(
                    ['name' => $tagName],
                    [
                        'author_id' => Auth::guard()->id(),
                        'author_type' => User::class,
                        'status' => 'published',
                    ]
                );
            });

        $portfolio->tags()->sync($tags->pluck('id'));
    }

    public function getWidgetRecentPortfolios(Request $request): BaseHttpResponse
    {
        $limit = $request->integer('paginate', 10);
        $limit = $limit > 0 ? $limit : 10;

        $portfolios = $this->portfolioRepository->advancedGet([
            'condition' => [
                'status' => 'published',
            ],
            'order_by' => [
                'created_at' => 'DESC',
            ],
            'take' => $limit,
            'with' => ['slugable'],
        ]);

        return $this
            ->httpResponse()
            ->setData(view('plugins/portfolio::portfolios.widgets.recent-portfolios', compact('portfolios', 'limit'))->render());
    }
}
