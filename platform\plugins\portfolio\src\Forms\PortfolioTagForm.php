<?php

namespace <PERSON><PERSON>qi\Portfolio\Forms;

use Shaqi\Base\Forms\FieldOptions\DescriptionFieldOption;
use Shaqi\Base\Forms\FieldOptions\NameFieldOption;
use <PERSON>haqi\Base\Forms\FieldOptions\StatusFieldOption;
use <PERSON>haqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TextareaField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\FormAbstract;
use Shaqi\Portfolio\Http\Requests\PortfolioTagRequest;
use Shaqi\Portfolio\Models\PortfolioTag;

class PortfolioTagForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(PortfolioTag::class)
            ->setValidatorClass(PortfolioTagRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required()->maxLength(120))
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
