<?php

namespace <PERSON><PERSON>qi\Portfolio\Models;

use <PERSON>haqi\Base\Casts\SafeContent;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Models\BaseModel;
use Shaqi\Base\Supports\TreeCategory\Contracts\HasTreeCategoryContract;
use Shaqi\Base\Supports\TreeCategory\HasTreeCategory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PortfolioCategory extends BaseModel implements HasTreeCategoryContract
{
    use HasTreeCategory;

    protected $table = 'portfolio_categories';

    protected $fillable = [
        'name',
        'description',
        'parent_id',
        'icon',
        'is_featured',
        'order',
        'is_default',
        'status',
        'author_id',
        'author_type',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'is_default' => 'bool',
        'is_featured' => 'bool',
        'order' => 'int',
    ];

    protected static function booted(): void
    {
        static::deleted(function (PortfolioCategory $category): void {
            $category->children()->each(fn (Model $child) => $child->delete());

            $category->portfolios()->detach();
        });
    }

    public function portfolios(): BelongsToMany
    {
        return $this->belongsToMany(Portfolio::class, 'portfolio_category_pivot', 'category_id', 'portfolio_id');
    }
}
