<?php

return [
    [
        'name' => 'Portfolio',
        'flag' => 'plugins.portfolio',
        'parent_flag' => 'core.cms',
    ],
    [
        'name' => 'Portfolios',
        'flag' => 'portfolios.index',
        'parent_flag' => 'plugins.portfolio',
    ],
    [
        'name' => 'Create',
        'flag' => 'portfolios.create',
        'parent_flag' => 'portfolios.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'portfolios.edit',
        'parent_flag' => 'portfolios.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'portfolios.destroy',
        'parent_flag' => 'portfolios.index',
    ],

    [
        'name' => 'Portfolio Categories',
        'flag' => 'portfolio-categories.index',
        'parent_flag' => 'plugins.portfolio',
    ],
    [
        'name' => 'Create',
        'flag' => 'portfolio-categories.create',
        'parent_flag' => 'portfolio-categories.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'portfolio-categories.edit',
        'parent_flag' => 'portfolio-categories.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'portfolio-categories.destroy',
        'parent_flag' => 'portfolio-categories.index',
    ],

    [
        'name' => 'Portfolio Tags',
        'flag' => 'portfolio-tags.index',
        'parent_flag' => 'plugins.portfolio',
    ],
    [
        'name' => 'Create',
        'flag' => 'portfolio-tags.create',
        'parent_flag' => 'portfolio-tags.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'portfolio-tags.edit',
        'parent_flag' => 'portfolio-tags.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'portfolio-tags.destroy',
        'parent_flag' => 'portfolio-tags.index',
    ],
    [
        'name' => 'Portfolio',
        'flag' => 'portfolio.settings',
        'parent_flag' => 'settings.others',
    ],
    [
        'name' => 'Export Portfolios',
        'flag' => 'portfolios.export',
        'parent_flag' => 'tools.data-synchronize',
    ],
    [
        'name' => 'Import Portfolios',
        'flag' => 'portfolios.import',
        'parent_flag' => 'tools.data-synchronize',
    ],
];
