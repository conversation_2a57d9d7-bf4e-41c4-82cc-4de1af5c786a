<?php

namespace <PERSON>haqi\Portfolio\Enums;

use <PERSON><PERSON><PERSON>\Base\Supports\Enum;

/**
 * @method static PortfolioStatusEnum PUBLISHED()
 * @method static PortfolioStatusEnum DRAFT()
 * @method static PortfolioStatusEnum PENDING()
 */
class PortfolioStatusEnum extends Enum
{
    public const PUBLISHED = 'published';
    public const DRAFT = 'draft';
    public const PENDING = 'pending';

    public static $langPath = 'plugins/portfolio::portfolios.statuses';
}
