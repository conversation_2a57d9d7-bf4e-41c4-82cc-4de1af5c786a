<?php

namespace <PERSON><PERSON><PERSON>\Portfolio\Listeners;

use <PERSON>haqi\Base\Events\DeletedContentEvent;
use Exception;

class DeletedContentListener
{
    public function handle(DeletedContentEvent $event): void
    {
        try {
            do_action(BASE_ACTION_AFTER_DELETE_CONTENT, $event->screen, $event->request, $event->data);
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}
