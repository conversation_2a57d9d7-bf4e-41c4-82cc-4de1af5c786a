[2025-07-15 22:36:31] local.ERROR: View [partials.portfolios-short-code-admin-config] not found. {"exception":"[object] (InvalidArgumentException(code: 0): View [partials.portfolios-short-code-admin-config] not found. at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:139)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(92): Illuminate\\View\\FileViewFinder->findInPaths('partials.portfo...', Array)
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(76): Illuminate\\View\\FileViewFinder->findNamespacedView('plugins/portfol...')
#2 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\Factory.php(33): Illuminate\\View\\FileViewFinder->find('plugins/portfol...')
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1062): Shaqi\\Shortcode\\View\\Factory->make('plugins/portfol...', Array, Array)
#4 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\HookServiceProvider.php(58): view('plugins/portfol...')
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\Portfolio\\Providers\\HookServiceProvider->boot()
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\Portfolio\\Providers\\HookServiceProvider))
#12 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\PortfolioServiceProvider.php(211): Illuminate\\Foundation\\Application->register(Object(Shaqi\\Portfolio\\Providers\\HookServiceProvider))
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1152): Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider->Shaqi\\Portfolio\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#14 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\PortfolioServiceProvider.php(205): Illuminate\\Foundation\\Application->booted(Object(Closure))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider->boot()
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider))
#22 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(98): Illuminate\\Foundation\\Application->register(Object(Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider))
#23 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('portfolio')
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#25 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#30 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 {main}
"} 
