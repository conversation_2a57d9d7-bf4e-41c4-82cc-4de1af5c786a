<?php

namespace <PERSON><PERSON><PERSON>\Portfolio\Tables;

use <PERSON><PERSON><PERSON>\Portfolio\Models\PortfolioTag;
use Shaqi\Table\Abstracts\TableAbstract;
use Shaqi\Table\Actions\DeleteAction;
use <PERSON>haqi\Table\Actions\EditAction;
use <PERSON><PERSON><PERSON>\Table\BulkActions\DeleteBulkAction;
use Shaqi\Table\BulkChanges\CreatedAtBulkChange;
use Shaqi\Table\BulkChanges\NameBulkChange;
use Shaqi\Table\BulkChanges\StatusBulkChange;
use Shaqi\Table\Columns\CreatedAtColumn;
use Shaqi\Table\Columns\IdColumn;
use Shaqi\Table\Columns\NameColumn;
use Shaqi\Table\Columns\StatusColumn;
use Shaqi\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class PortfolioTagTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(PortfolioTag::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('portfolio-tags.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('portfolio-tags.edit'),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('portfolio-tags.edit'),
                DeleteAction::make()->route('portfolio-tags.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('portfolio-tags.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'created_at',
                        'status',
                    ]);
            });
    }
}
