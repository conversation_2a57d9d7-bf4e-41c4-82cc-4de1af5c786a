<?php

namespace <PERSON><PERSON>qi\Portfolio\Models;

use <PERSON>haqi\ACL\Models\User;
use Shaqi\Base\Casts\SafeContent;
use Shaqi\Base\Models\BaseModel;
use <PERSON>haqi\Portfolio\Enums\PortfolioStatusEnum;
use <PERSON>haqi\Revision\RevisionableTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Portfolio extends BaseModel
{
    use RevisionableTrait;

    protected $table = 'portfolios';

    protected bool $revisionEnabled = true;

    protected bool $revisionCleanup = true;

    protected int $historyLimit = 20;

    protected array $dontKeepRevisionOf = [
        'content',
        'views',
    ];

    protected $fillable = [
        'name',
        'description',
        'content',
        'image',
        'logo',
        'website_link',
        'features',
        'gallery',
        'is_featured',
        'status',
        'author_id',
        'author_type',
    ];

    protected static function booted(): void
    {
        static::deleted(function (self $portfolio): void {
            $portfolio->categories()->detach();
            $portfolio->tags()->detach();
        });

        static::creating(function (self $portfolio): void {
            $portfolio->author_id = $portfolio->author_id ?: auth()->id();
            $portfolio->author_type = $portfolio->author_type ?: User::class;
        });
    }

    protected $casts = [
        'status' => PortfolioStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'features' => 'array',
        'gallery' => 'array',
        'is_featured' => 'boolean',
    ];

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(PortfolioTag::class, 'portfolio_tags', 'portfolio_id', 'tag_id');
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(PortfolioCategory::class, 'portfolio_category_pivot', 'portfolio_id', 'category_id');
    }

    public function author(): MorphTo
    {
        return $this->morphTo()->withDefault();
    }

    protected function firstCategory(): Attribute
    {
        return Attribute::get(function (): ?PortfolioCategory {
            $this->loadMissing('categories');

            return $this->categories->first();
        });
    }

    protected function firstTag(): Attribute
    {
        return Attribute::get(function (): ?PortfolioTag {
            $this->loadMissing('tags');

            return $this->tags->first();
        });
    }
}
