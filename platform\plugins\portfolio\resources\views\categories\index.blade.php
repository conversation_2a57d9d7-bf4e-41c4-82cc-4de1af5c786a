@extends(BaseHelper::getAdminMasterLayoutTemplate())
@section('content')
    <div class="row">
        <div class="col-md-9">
            <div id="main-tree-category">
                <div class="tree-category-container">
                    @include('core/base::forms.partials.tree-categories', $options)
                </div>
            </div>
        </div>
        <div class="col-md-3 right-sidebar d-flex flex-column-reverse flex-md-column">
            <div class="form-body-right-sidebar">
                <div class="widget meta-boxes">
                    <div class="widget-title">
                        <h4>{{ trans('plugins/portfolio::categories.create') }}</h4>
                    </div>
                    <div class="widget-body">
                        <div class="form-category-content">
                            {!! $form !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
